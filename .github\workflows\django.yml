name: Heibooky CI/CD Pipeline

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.11']

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_heibooky
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip setuptools wheel
        pip install -r requirements.txt
        pip install pytest-cov pytest-django black

    - name: Clean Python cache files
      run: |
        find . -type f -name "*.pyc" -delete
        find . -type d -name "__pycache__" -exec rm -rf {} + || true

    - name: Create test environment file
      run: |
        cd heibooky
        # Create necessary directories with proper permissions
        mkdir -p /tmp/test_logs
        mkdir -p /tmp/test_media
        
        cat > .env << EOF
        # Minimal environment variables for CI testing
        SECRET_KEY=test-secret-key-for-ci
        DEBUG=False
        EOF

    - name: Run code formatting checks
      run: |
        black --check .

    - name: Validate Django configuration
      env:
        SECRET_KEY: test-secret-key-for-ci
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/test_heibooky
        REDIS_URL: redis://localhost:6379/0
        DEBUG: "False"
      run: |
        cd heibooky
        python manage.py check --settings=heibooky.test_settings

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

  build:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    env:
      DOCKER_BUILDKIT: 1
      BUILDKIT_PROGRESS: plain

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        driver-opts: |
          network=host
        buildkitd-flags: |
          --allow-insecure-entitlement=network.host
          --allow-insecure-entitlement=security.insecure

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: |
          type=gha,scope=build-cache
          type=gha,scope=deps-cache
          type=gha,scope=runtime-cache
        cache-to: |
          type=gha,mode=max,scope=build-cache
          type=gha,mode=max,scope=deps-cache
          type=gha,mode=max,scope=runtime-cache
        platforms: linux/amd64
        build-args: |
          BUILDKIT_INLINE_CACHE=1

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}@${{ steps.build.outputs.digest }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Upload SBOM
      uses: actions/upload-artifact@v4
      with:
        name: sbom
        path: sbom.spdx.json

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}@${{ steps.build.outputs.digest }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Install Cosign
      uses: sigstore/cosign-installer@v3.1.1
      with:
        cosign-release: 'v2.2.0'

    - name: Sign container image
      env:
        COSIGN_EXPERIMENTAL: 1
      run: |
        cosign sign --yes ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}@${{ steps.build.outputs.digest }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Doppler CLI
      uses: dopplerhq/cli-action@v3

    - name: Deploy to staging server
      env:
        DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_STG }}
        AWS_PRIVATE_KEY: ${{ secrets.AWS_PRIVATE_KEY }}
        AWS_HOST: ${{ secrets.AWS_HOST_STG }}
        AWS_USER: ${{ secrets.AWS_USER }}
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        # Setup SSH
        mkdir -p ~/.ssh
        echo "$AWS_PRIVATE_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H $AWS_HOST >> ~/.ssh/known_hosts

        # Deploy to staging
        ssh $AWS_USER@$AWS_HOST << 'EOF'
          set -e
          cd /opt/heibooky

          # Pull latest code
          git pull origin develop

          # Set environment variables
          export DOPPLER_TOKEN=${{ secrets.DOPPLER_TOKEN_STG }}
          export IMAGE_TAG=${{ needs.build.outputs.image-tag }}

          # Run deployment script
          ./scripts/deploy.sh staging
        EOF

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install Doppler CLI
      uses: dopplerhq/cli-action@v3

    - name: Deploy to production server
      env:
        DOPPLER_TOKEN: ${{ secrets.DOPPLER_TOKEN_PRD }}
        AWS_PRIVATE_KEY: ${{ secrets.AWS_PRIVATE_KEY }}
        AWS_HOST: ${{ secrets.AWS_HOST_PRD }}
        AWS_USER: ${{ secrets.AWS_USER }}
        IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
      run: |
        # Setup SSH
        mkdir -p ~/.ssh
        echo "$AWS_PRIVATE_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H $AWS_HOST >> ~/.ssh/known_hosts

        # Deploy to production
        ssh $AWS_USER@$AWS_HOST << 'EOF'
          set -e
          cd /opt/heibooky

          # Pull latest code
          git pull origin main

          # Set environment variables
          export DOPPLER_TOKEN=${{ secrets.DOPPLER_TOKEN_PRD }}
          export IMAGE_TAG=${{ needs.build.outputs.image-tag }}

          # Run deployment script
          ./scripts/deploy.sh production
        EOF

    - name: Create GitHub Release
      if: github.ref == 'refs/heads/main'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          Automated release from main branch

          **Changes:**
          ${{ github.event.head_commit.message }}

          **Docker Image:**
          `${{ needs.build.outputs.image-tag }}`
        draft: false
        prerelease: false

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Determine deployment environment and status
      id: deployment-info
      run: |
        if [[ "${{ needs.deploy-staging.result }}" == "success" ]]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "status=success" >> $GITHUB_OUTPUT
          echo "environment_url=https://staging.heibooky.com" >> $GITHUB_OUTPUT
        elif [[ "${{ needs.deploy-production.result }}" == "success" ]]; then
          echo "environment=production" >> $GITHUB_OUTPUT
          echo "status=success" >> $GITHUB_OUTPUT
          echo "environment_url=https://heibooky.com" >> $GITHUB_OUTPUT
        elif [[ "${{ needs.deploy-staging.result }}" == "failure" ]]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "status=failure" >> $GITHUB_OUTPUT
          echo "environment_url=https://staging.heibooky.com" >> $GITHUB_OUTPUT
        elif [[ "${{ needs.deploy-production.result }}" == "failure" ]]; then
          echo "environment=production" >> $GITHUB_OUTPUT
          echo "status=failure" >> $GITHUB_OUTPUT
          echo "environment_url=https://heibooky.com" >> $GITHUB_OUTPUT
        else
          echo "environment=unknown" >> $GITHUB_OUTPUT
          echo "status=skipped" >> $GITHUB_OUTPUT
          echo "environment_url=" >> $GITHUB_OUTPUT
        fi

    - name: Send success notification email
      if: steps.deployment-info.outputs.status == 'success'
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: smtp.gmail.com
        server_port: 587
        username: ${{ secrets.SMTP_USERNAME }}
        password: ${{ secrets.SMTP_PASSWORD }}
        subject: "✅ Heibooky Deployment Successful - ${{ steps.deployment-info.outputs.environment }}"
        to: <EMAIL>
        from: ${{ secrets.SMTP_USERNAME }}
        html_body: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Deployment Success</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0fff4; }
                  .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                  .header { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin-bottom: 20px; text-align: center; }
                  .content { line-height: 1.6; }
                  .details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
                  .button { display: inline-block; background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                  .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1>🚀 Deployment Successful!</h1>
                      <p>Heibooky has been successfully deployed to <strong>${{ steps.deployment-info.outputs.environment }}</strong></p>
                  </div>
                  
                  <div class="content">
                      <h2>Deployment Details</h2>
                      <div class="details">
                          <p><strong>Environment:</strong> ${{ steps.deployment-info.outputs.environment }}</p>
                          <p><strong>Branch:</strong> ${{ github.ref_name }}</p>
                          <p><strong>Commit:</strong> ${{ github.sha }}</p>
                          <p><strong>Commit Message:</strong> ${{ github.event.head_commit.message }}</p>
                          <p><strong>Triggered By:</strong> ${{ github.actor }}</p>
                          <p><strong>Workflow Run:</strong> #${{ github.run_number }}</p>
                          <p><strong>Deployment Time:</strong> ${{ github.event.head_commit.timestamp }}</p>
                      </div>
                      
                      <p>The application is now live and accessible at:</p>
                      <a href="${{ steps.deployment-info.outputs.environment_url }}" class="button">Visit Application</a>
                      
                      <h3>Quick Links</h3>
                      <ul>
                          <li><a href="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}">View Workflow Run</a></li>
                          <li><a href="https://github.com/${{ github.repository }}/commit/${{ github.sha }}">View Commit</a></li>
                          <li><a href="${{ steps.deployment-info.outputs.environment_url }}/admin/">Admin Panel</a></li>
                          <li><a href="https://grafana.heibooky.com">Monitoring Dashboard</a></li>
                      </ul>
                  </div>
                  
                  <div class="footer">
                      <p>This is an automated notification from the Heibooky CI/CD pipeline.</p>
                      <p>GitHub Repository: <a href="https://github.com/${{ github.repository }}">${{ github.repository }}</a></p>
                  </div>
              </div>
          </body>
          </html>

    - name: Send failure notification email
      if: steps.deployment-info.outputs.status == 'failure'
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: smtp.gmail.com
        server_port: 587
        username: ${{ secrets.SMTP_USERNAME }}
        password: ${{ secrets.SMTP_PASSWORD }}
        subject: "❌ Heibooky Deployment Failed - ${{ steps.deployment-info.outputs.environment }}"
        to: <EMAIL>
        from: ${{ secrets.SMTP_USERNAME }}
        html_body: |
          <!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>Deployment Failure</title>
              <style>
                  body { font-family: Arial, sans-serif; margin: 20px; background-color: #fff5f5; }
                  .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                  .header { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 5px; margin-bottom: 20px; text-align: center; }
                  .content { line-height: 1.6; }
                  .details { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
                  .button { display: inline-block; background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                  .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }
                  .alert { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
              </style>
          </head>
          <body>
              <div class="container">
                  <div class="header">
                      <h1>⚠️ Deployment Failed!</h1>
                      <p>Heibooky deployment to <strong>${{ steps.deployment-info.outputs.environment }}</strong> has failed</p>
                  </div>
                  
                  <div class="content">
                      <div class="alert">
                          <strong>⚠️ Immediate Action Required</strong><br>
                          The deployment process encountered an error and needs attention.
                      </div>
                      
                      <h2>Failure Details</h2>
                      <div class="details">
                          <p><strong>Environment:</strong> ${{ steps.deployment-info.outputs.environment }}</p>
                          <p><strong>Branch:</strong> ${{ github.ref_name }}</p>
                          <p><strong>Commit:</strong> ${{ github.sha }}</p>
                          <p><strong>Commit Message:</strong> ${{ github.event.head_commit.message }}</p>
                          <p><strong>Triggered By:</strong> ${{ github.actor }}</p>
                          <p><strong>Workflow Run:</strong> #${{ github.run_number }}</p>
                          <p><strong>Failure Time:</strong> ${{ github.event.head_commit.timestamp }}</p>
                          <p><strong>Staging Status:</strong> ${{ needs.deploy-staging.result || 'not run' }}</p>
                          <p><strong>Production Status:</strong> ${{ needs.deploy-production.result || 'not run' }}</p>
                      </div>
                      
                      <h3>Next Steps</h3>
                      <ol>
                          <li>Check the workflow logs for detailed error information</li>
                          <li>Verify server connectivity and permissions</li>
                          <li>Review recent code changes for potential issues</li>
                          <li>Check monitoring dashboards for system health</li>
                          <li>Consider rolling back if necessary</li>
                      </ol>
                      
                      <a href="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}" class="button">View Failure Logs</a>
                      
                      <h3>Quick Links</h3>
                      <ul>
                          <li><a href="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}">View Workflow Run</a></li>
                          <li><a href="https://github.com/${{ github.repository }}/commit/${{ github.sha }}">View Commit</a></li>
                          <li><a href="https://grafana.heibooky.com">Monitoring Dashboard</a></li>
                          <li><a href="https://github.com/${{ github.repository }}/issues/new">Create Issue</a></li>
                      </ul>
                  </div>
                  
                  <div class="footer">
                      <p>This is an automated notification from the Heibooky CI/CD pipeline.</p>
                      <p>GitHub Repository: <a href="https://github.com/${{ github.repository }}">${{ github.repository }}</a></p>
                  </div>
              </div>
          </body>
          </html>

    - name: Send Slack notification (backup)
      if: always() && steps.deployment-info.outputs.status != 'skipped'
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ steps.deployment-info.outputs.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        custom_payload: |
          {
            "blocks": [
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "${{ steps.deployment-info.outputs.status == 'success' && '✅' || '❌' }} *Heibooky Deployment ${{ steps.deployment-info.outputs.status == 'success' && 'Successful' || 'Failed' }}*\n*Environment:* ${{ steps.deployment-info.outputs.environment }}\n*Branch:* ${{ github.ref_name }}\n*Commit:* <https://github.com/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}>\n*Triggered by:* ${{ github.actor }}"
                }
              },
              {
                "type": "actions",
                "elements": [
                  {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "View Logs"
                    },
                    "url": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  },
                  {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "Visit App"
                    },
                    "url": "${{ steps.deployment-info.outputs.environment_url }}"
                  }
                ]
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Update deployment status
      if: always() && steps.deployment-info.outputs.status != 'skipped'
      run: |
        echo "Deployment notification sent for ${{ steps.deployment-info.outputs.environment }} environment"
        echo "Status: ${{ steps.deployment-info.outputs.status }}"
        
        if [[ "${{ steps.deployment-info.outputs.status }}" == "failure" ]]; then
          echo "::error::Deployment to ${{ steps.deployment-info.outputs.environment }} failed"
          exit 1
        fi
